package com.iflytek.fpva.cdc.reportcard.controller;


import com.iflytek.fpva.cdc.common.annotation.OperationLogAnnotation;
import com.iflytek.fpva.cdc.entity.TbCdcewFileUpload;
import com.iflytek.fpva.cdc.reportcard.dto.InfectedReportCardExcelDTO;
import com.iflytek.fpva.cdc.reportcard.service.InfectedReportCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/pt/{version}/infected/reportCard")
public class InfectedReportCardController {

    private InfectedReportCardService infectedReportCardService;

    @Autowired
    public void setInfectedReportCardService(InfectedReportCardService infectedReportCardService) {
        this.infectedReportCardService = infectedReportCardService;
    }

    /**
     * 新增法定传染病报告卡 非法定传染病 其他传染病
     * @return
     */
    @PostMapping("/create")
    public Boolean create(@RequestBody InfectedReportCardExcelDTO dto,
                          @RequestParam String loginUserId){
        return infectedReportCardService.create(dto,loginUserId);
    }

    /**
     * 上传文件导入数据
     * @param file
     * @return
     */
    @PostMapping(value = "/uploadFileAndInsert")
    @OperationLogAnnotation(operationName = "报告卡信息填报-上传导入excel并插入记录")
    public TbCdcewFileUpload uploadFile(@RequestParam MultipartFile file){
        return infectedReportCardService.uploadFileAndInsert(file);
    }

    @GetMapping("/checkPop")
    public Boolean checkPop(@RequestParam String loginUserId){
        return infectedReportCardService.checkPop(loginUserId);
    }

}
