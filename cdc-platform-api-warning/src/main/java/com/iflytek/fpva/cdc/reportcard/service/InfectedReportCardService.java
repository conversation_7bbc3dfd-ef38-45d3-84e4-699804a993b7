package com.iflytek.fpva.cdc.reportcard.service;

import com.iflytek.fpva.cdc.entity.TbCdcewFileUpload;
import com.iflytek.fpva.cdc.reportcard.dto.InfectedReportCardExcelDTO;
import org.springframework.web.multipart.MultipartFile;

public interface InfectedReportCardService {
    Boolean create(InfectedReportCardExcelDTO dto, String loginUserId);

    TbCdcewFileUpload uploadFileAndInsert(MultipartFile file);

    Boolean checkPop(String loginUserId);
}
