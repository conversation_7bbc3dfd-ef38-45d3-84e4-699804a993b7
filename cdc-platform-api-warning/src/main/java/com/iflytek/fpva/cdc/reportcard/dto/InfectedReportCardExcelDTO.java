package com.iflytek.fpva.cdc.reportcard.dto;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import lombok.Data;

import java.util.Date;

@Data
public class InfectedReportCardExcelDTO {

    /**
     * 卡片ID
     */
    @ExcelColumn(column = 1, name = "卡片ID", required = true, unique = true)
    private String reportCardId;

    /**
     * 卡片编号
     */
    @ExcelColumn(column = 2, name = "卡片编号")
    private String reportCardCode;

    /**
     * 卡片状态
     */
    @ExcelColumn(column = 3, name = "卡片状态", required = true)
    private String status;

    /**
     * 报告类别
     */
    @ExcelColumn(column = 4, name = "报告类别")
    private String reportClass;


    /**
     * 患者姓名
     */
    @ExcelColumn(column = 5, name = "*患者姓名", required = true)
    private String name;

    /**
     * 患儿家长姓名
     */
    @ExcelColumn(column = 6, name = "患儿家长姓名")
    private String parentName;

    /**
     * 有效证件类型
     */
    @ExcelColumn(column = 7, name = "有效证件类型", required = true)
    private String validCertType;

    /**
     * 有效证件号码
     */
    @ExcelColumn(column = 8, name = "有效证件号码", required = true, strip = {"'", "\""})
    private String validCertNumber;

    /**
     * 性别
     */
    @ExcelColumn(column = 9, name = "性别", required = true)
    private String sexDesc;

    /**
     * 出生日期
     */
    @ExcelColumn(column = 10, name = "出生日期", required = true)
    private Date birthday;

    /**
     * 年龄
     */
    @ExcelColumn(column = 11, name = "年龄")
    private String age;

    /**
     * 年龄单位
     */
    @ExcelColumn(column = 12, name = "年龄单位")
    private String ageUnit;


    /**
     * 患者工作单位
     */
    @ExcelColumn(column = 13, name = "患者工作单位")
    private String company;

    /**
     * 联系电话
     */
    @ExcelColumn(column = 14, name = "*联系电话", required = true, strip = {"'", "\""})
    private String phone;

    /**
     * 病人属于
     */
    @ExcelColumn(column = 15, name = "病人属于", required = true)
    private String attribution;

    /**
     * 现住地址国标
     */
    @ExcelColumn(column = 16, name = "现住地址国标", required = true)
    private String addressCode;

    /**
     * 现住详细地址
     */
    @ExcelColumn(column = 17, name = "现住详细地址", required = true)
    private String addressName;

    /**
     * 人群分类
     */
    @ExcelColumn(column = 18, name = "人群分类", required = true)
    private String humanCategory;

    /**
     * 症状体征
     */
    @ExcelColumn(column = 19, name = "症状体征", required = true)
    private String symptomSign;

    /**
     * 病例分类
     */
    @ExcelColumn(column = 20, name = "病例分类", required = true)
    private String casesCategory;

    /**
     * 病例分类2
     */
    @ExcelColumn(column = 21, name = "病例分类2")
    private String casesCategory2;

    /**
     * 发病日期
     */
    @ExcelColumn(column = 22, name = "发病日期", required = true)
    private Date onsetDate;

    /**
     * 诊断时间
     */
    @ExcelColumn(column = 23, name = "诊断时间", required = true)
    private Date diagnoseTime;

    /**
     * 死亡日期
     */
    @ExcelColumn(column = 24, name = "死亡日期")
    private Date deathDate;

    /**
     * 疾病名称
     */
    @ExcelColumn(column = 25, name = "*疾病名称", required = true)
    private String diseaseName;

    /**
     * 接触史
     */
    @ExcelColumn(column = 26, name = "接触史")
    private String contactHistory;


    @ExcelColumn(column = 27, name = "是否隔离")
    private String quarantineFlag;

    /**
     * 订正前病种
     */
    @ExcelColumn(column = 28, name = "订正前病种")
    private String revisedPreviousDisease;

    /**
     * 订正前诊断时间
     */
    @ExcelColumn(column = 29, name = "订正前诊断时间")
    private Date revisedPreviousDiagnoseTime;

    /**
     * 订正前终审时间
     */
    @ExcelColumn(column = 30, name = "订正前终审时间")
    private Date revisedPreviousCheckTime;

    /**
     * 填卡医生
     */
    @ExcelColumn(column = 31, name = "填卡医生", required = true)
    private String fillDoctor;

    /**
     * 医生填卡日期
     */
    @ExcelColumn(column = 32, name = "医生填卡日期", required = true)
    private Date fillDate;

    /**
     * 报告单位地区编码
     */
    @ExcelColumn(column = 33, name = "报告单位地区编码")
    private String unitCode;

    /**
     * 报告单位
     */
    @ExcelColumn(column = 34, name = "报告单位", required = true)
    private String unitName;

    /**
     * 单位类型
     */
    @ExcelColumn(column = 35, name = "单位类型")
    private String unitType;

    /**
     * 报告卡录入时间
     */
    @ExcelColumn(column = 36, name = "报告卡录入时间", required = true)
    private Date recordTime;

    /**
     * 录卡用户
     */
    @ExcelColumn(column = 37, name = "录卡用户", required = true)
    private String recordUser;

    /**
     * 录卡用户所属单位
     */
    @ExcelColumn(column = 38, name = "录卡用户所属单位", required = true)
    private String recordUserCompany;

    /**
     * 县区审核时间
     */
    @ExcelColumn(column = 39, name = "县区审核时间")
    private Date districtCheckTime;

    /**
     * 地市审核时间
     */
    @ExcelColumn(column = 40, name = "地市审核时间")
    private Date cityCheckTime;

    /**
     * 省市审核时间
     */
    @ExcelColumn(column = 41, name = "省市审核时间")
    private Date provinceCheckTime;

    /**
     * 审核状态
     */
    @ExcelColumn(column = 42, name = "*审核状态", required = true)
    private String checkStatus;

    /**
     * 订正报告时间
     */
    @ExcelColumn(column = 43, name = "订正报告时间")
    private Date revisedReportTime;

    /**
     * 订正终审时间
     */
    @ExcelColumn(column = 44, name = "订正终审时间")
    private Date revisedFinalCheckTime;

    /**
     * 终审死亡时间
     */
    @ExcelColumn(column = 45, name = "终审死亡时间")
    private Date finalCheckDeathTime;

    /**
     * 订正用户
     */
    @ExcelColumn(column = 46, name = "订正用户")
    private String revisedUser;

    /**
     * 订正用户所属单位
     */
    @ExcelColumn(column = 47, name = "订正用户所属单位")
    private String revisedUserCompany;

    /**
     * 删除时间
     */
    @ExcelColumn(column = 48, name = "删除时间")
    private Date deleteTime;

    /**
     * 删除用户
     */
    @ExcelColumn(column = 49, name = "删除用户")
    private String deleteUser;

    /**
     * 删除用户所属单位
     */
    @ExcelColumn(column = 50, name = "删除用户所属单位")
    private String deleteUserCompany;

    /**
     * 删除原因
     */
    @ExcelColumn(column = 51, name = "删除原因")
    private String deleteReason;

    /**
     * 备注
     */
    @ExcelColumn(column = 52, name = "备注")
    private String remark;

    /**
     * 疾病编码
     */
    private String diseaseCode;


    /**
     * 报告卡类型区分 01 法定传染病 02 非法定传染病 09 其他疾病 10 其他信息报送
     */
    private String reportCardClassType;
}
